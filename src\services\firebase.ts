
import { db } from '@/lib/firebase';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { Tour, Review, BlogPost, Booking, ContactMessage, UserProfile, Destination, GalleryImage } from '@/types/firebase';

export class FirebaseService {
  // Gallery Images
  static async getGalleryImages() {
    try {
      const querySnapshot = await getDocs(collection(db, 'galleryImages'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting gallery images:', error);
      throw error;
    }
  }

  static async createGalleryImage(imageData: Omit<GalleryImage, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'galleryImages'), {
        ...imageData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating gallery image:', error);
      throw error;
    }
  }

  static async updateGalleryImage(id: string, imageData: Partial<GalleryImage>) {
    try {
      await updateDoc(doc(db, 'galleryImages', id), {
        ...imageData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating gallery image:', error);
      throw error;
    }
  }

  static async deleteGalleryImage(id: string) {
    try {
      await deleteDoc(doc(db, 'galleryImages', id));
    } catch (error) {
      console.error('Error deleting gallery image:', error);
      throw error;
    }
  }

  // Blog Posts
  static async getBlogPosts() {
    try {
      const querySnapshot = await getDocs(collection(db, 'blogPosts'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting blog posts:', error);
      throw error;
    }
  }

  static async createBlogPost(postData: Omit<BlogPost, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'blogPosts'), {
        ...postData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating blog post:', error);
      throw error;
    }
  }

  static async updateBlogPost(id: string, postData: Partial<BlogPost>) {
    try {
      await updateDoc(doc(db, 'blogPosts', id), {
        ...postData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating blog post:', error);
      throw error;
    }
  }

  static async deleteBlogPost(id: string) {
    try {
      await deleteDoc(doc(db, 'blogPosts', id));
    } catch (error) {
      console.error('Error deleting blog post:', error);
      throw error;
    }
  }

  // Reviews
  static async getAllReviews() {
    try {
      const querySnapshot = await getDocs(collection(db, 'reviews'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting all reviews:', error);
      throw error;
    }
  }

  static async getReviews(tourId: string) {
    try {
      const q = query(collection(db, 'reviews'), where('tourId', '==', tourId));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting reviews:', error);
      throw error;
    }
  }

  static async createReview(reviewData: Omit<Review, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'reviews'), reviewData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating review:', error);
      throw error;
    }
  }

  static async updateReview(id: string, reviewData: Partial<Review>) {
    try {
      await updateDoc(doc(db, 'reviews', id), {
        ...reviewData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating review:', error);
      throw error;
    }
  }

  static async deleteReview(id: string) {
    try {
      await deleteDoc(doc(db, 'reviews', id));
    } catch (error) {
      console.error('Error deleting review:', error);
      throw error;
    }
  }

  // Tours
  static async getTours() {
    try {
      const querySnapshot = await getDocs(collection(db, 'tours'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting tours:', error);
      throw error;
    }
  }

  static async getTour(id: string) {
    try {
      const docSnap = await getDoc(doc(db, 'tours', id));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      }
      return null;
    } catch (error) {
      console.error('Error getting tour:', error);
      throw error;
    }
  }

  static async createTour(tourData: Omit<Tour, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'tours'), tourData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating tour:', error);
      throw error;
    }
  }

  static async updateTour(id: string, tourData: Partial<Tour>) {
    try {
      await updateDoc(doc(db, 'tours', id), {
        ...tourData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating tour:', error);
      throw error;
    }
  }

  static async deleteTour(id: string) {
    try {
      await deleteDoc(doc(db, 'tours', id));
    } catch (error) {
      console.error('Error deleting tour:', error);
      throw error;
    }
  }

  static async searchTours(searchTerm: string, filters?: any) {
    try {
      let q = collection(db, 'tours');
      const querySnapshot = await getDocs(q);
      let results = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      // Apply search term filter
      if (searchTerm) {
        results = results.filter(tour => 
          tour.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tour.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tour.destinations?.some((dest: string) => dest.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      }

      // Apply other filters
      if (filters) {
        if (filters.category) {
          results = results.filter(tour => tour.category === filters.category);
        }
        if (filters.destination) {
          results = results.filter(tour => 
            tour.destinations?.includes(filters.destination) || 
            tour.location?.includes(filters.destination)
          );
        }
        if (filters.duration) {
          results = results.filter(tour => tour.duration?.includes(filters.duration));
        }
        if (filters.priceRange) {
          results = results.filter(tour => 
            tour.price >= filters.priceRange.min && 
            tour.price <= filters.priceRange.max
          );
        }
      }

      return results;
    } catch (error) {
      console.error('Error searching tours:', error);
      throw error;
    }
  }

  // Users
  static async getAllUsers() {
    try {
      const querySnapshot = await getDocs(collection(db, 'users'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting users:', error);
      throw error;
    }
  }

  static async getUsers() {
    return this.getAllUsers();
  }

  static async getUserProfile(uid: string) {
    try {
      const docSnap = await getDoc(doc(db, 'users', uid));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      }
      return null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw error;
    }
  }

  static async updateUserProfile(uid: string, profileData: Partial<UserProfile>) {
    try {
      await updateDoc(doc(db, 'users', uid), {
        ...profileData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  static async deleteUser(uid: string) {
    try {
      await deleteDoc(doc(db, 'users', uid));
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  // Bookings
  static async getAllBookings() {
    try {
      const querySnapshot = await getDocs(collection(db, 'bookings'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting bookings:', error);
      throw error;
    }
  }

  static async getBookings() {
    return this.getAllBookings();
  }

  static async getBooking(id: string) {
    try {
      const docSnap = await getDoc(doc(db, 'bookings', id));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      }
      return null;
    } catch (error) {
      console.error('Error getting booking:', error);
      throw error;
    }
  }

  static async createBooking(bookingData: Omit<Booking, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'bookings'), bookingData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating booking:', error);
      throw error;
    }
  }

  static async updateBooking(id: string, bookingData: Partial<Booking>) {
    try {
      await updateDoc(doc(db, 'bookings', id), {
        ...bookingData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating booking:', error);
      throw error;
    }
  }

  // Messages
  static async getAllMessages() {
    try {
      const querySnapshot = await getDocs(collection(db, 'messages'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting messages:', error);
      throw error;
    }
  }

  static async getContactMessages() {
    return this.getAllMessages();
  }

  static async createMessage(messageData: Omit<ContactMessage, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'messages'), messageData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating message:', error);
      throw error;
    }
  }

  static async createContactMessage(messageData: Omit<ContactMessage, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'messages'), {
        ...messageData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating contact message:', error);
      throw error;
    }
  }

  static async updateContactMessage(id: string, messageData: Partial<ContactMessage>) {
    try {
      await updateDoc(doc(db, 'messages', id), {
        ...messageData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating contact message:', error);
      throw error;
    }
  }

  // Destinations
  static async createDestination(destinationData: Omit<Destination, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'destinations'), {
        ...destinationData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating destination:', error);
      throw error;
    }
  }

  // Notifications
  static async createNotification(notificationData: any) {
    try {
      const docRef = await addDoc(collection(db, 'notifications'), notificationData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Payment Transactions
  static async createPaymentTransaction(transactionData: any) {
    try {
      const docRef = await addDoc(collection(db, 'paymentTransactions'), transactionData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating payment transaction:', error);
      throw error;
    }
  }
}
