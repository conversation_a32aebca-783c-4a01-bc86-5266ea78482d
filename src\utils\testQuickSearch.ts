// Test utility for Quick Search Form functionality
export const testQuickSearchForm = () => {
  console.log('🔍 Testing Quick Search Form...');
  
  // Test 1: Check if form elements exist
  console.log('1️⃣ Checking form elements...');
  const form = document.querySelector('form');
  const destinationInput = document.querySelector('input[placeholder="Where to go?"]') as HTMLInputElement;
  const dateInput = document.querySelector('input[type="date"]') as HTMLInputElement;
  const travelersSelect = document.querySelector('select') as HTMLSelectElement;
  const searchButton = document.querySelector('button[type="submit"]') as HTMLButtonElement;
  
  if (form && destinationInput && dateInput && travelersSelect && searchButton) {
    console.log('✅ All form elements found');
  } else {
    console.log('❌ Some form elements missing');
    return false;
  }
  
  // Test 2: Check datalist for destinations
  console.log('2️⃣ Checking destination suggestions...');
  const datalist = document.querySelector('#destinations');
  if (datalist && datalist.children.length > 0) {
    console.log('✅ Destination suggestions available:', datalist.children.length);
  } else {
    console.log('❌ No destination suggestions found');
  }
  
  // Test 3: Check popular tags
  console.log('3️⃣ Checking popular tags...');
  const popularTags = document.querySelectorAll('button[type="button"]');
  if (popularTags.length > 0) {
    console.log('✅ Popular tags found:', popularTags.length);
  } else {
    console.log('❌ No popular tags found');
  }
  
  // Test 4: Simulate form interaction
  console.log('4️⃣ Testing form interaction...');
  try {
    // Fill destination
    destinationInput.value = 'Serengeti National Park';
    destinationInput.dispatchEvent(new Event('change', { bubbles: true }));
    
    // Set date (tomorrow)
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    dateInput.value = tomorrow.toISOString().split('T')[0];
    dateInput.dispatchEvent(new Event('change', { bubbles: true }));
    
    // Set travelers
    travelersSelect.value = '4';
    travelersSelect.dispatchEvent(new Event('change', { bubbles: true }));
    
    console.log('✅ Form filled successfully');
    console.log('📝 Form values:', {
      destination: destinationInput.value,
      date: dateInput.value,
      travelers: travelersSelect.value
    });
    
    return true;
  } catch (error) {
    console.log('❌ Form interaction failed:', error);
    return false;
  }
};

// Test function for popular tag clicks
export const testPopularTags = () => {
  console.log('🏷️ Testing popular tags...');
  
  const popularTags = document.querySelectorAll('button[type="button"]');
  
  if (popularTags.length === 0) {
    console.log('❌ No popular tags found');
    return false;
  }
  
  console.log('✅ Found', popularTags.length, 'popular tags');
  
  // Test clicking the first tag
  try {
    const firstTag = popularTags[0] as HTMLButtonElement;
    console.log('🖱️ Testing click on:', firstTag.textContent);
    
    // Simulate click
    firstTag.click();
    
    console.log('✅ Tag click successful');
    return true;
  } catch (error) {
    console.log('❌ Tag click failed:', error);
    return false;
  }
};

// Test URL parameter handling
export const testURLParameters = () => {
  console.log('🔗 Testing URL parameters...');
  
  const testParams = [
    { destination: 'Serengeti National Park', search: 'Serengeti National Park' },
    { destination: 'Ngorongoro Crater', date: '2024-07-15', travelers: '4' },
    { search: 'safari', destination: 'Tarangire National Park' }
  ];
  
  testParams.forEach((params, index) => {
    console.log(`${index + 1}️⃣ Testing parameters:`, params);
    
    const searchParams = new URLSearchParams(params);
    const testURL = `/tours?${searchParams.toString()}`;
    
    console.log('📍 Generated URL:', testURL);
  });
  
  console.log('✅ URL parameter generation working');
  return true;
};

// Comprehensive test runner
export const runQuickSearchTests = () => {
  console.log('🧪 Running Quick Search Form Tests...');
  console.log('=====================================');
  
  const results = {
    formElements: testQuickSearchForm(),
    popularTags: testPopularTags(),
    urlParameters: testURLParameters()
  };
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log('=====================================');
  console.log(`📊 Test Results: ${passedTests}/${totalTests} passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All Quick Search Form tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Check the logs above.');
  }
  
  return results;
};

// Make functions available globally for browser console testing
(window as any).testQuickSearch = {
  testQuickSearchForm,
  testPopularTags,
  testURLParameters,
  runQuickSearchTests
};
