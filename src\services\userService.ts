
import { FirebaseService } from './firebase';
import { UserProfile } from '@/types/firebase';

export class UserService {
  // Get user profile
  static async getUserProfile(uid: string) {
    try {
      return await FirebaseService.getUserProfile(uid);
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw error;
    }
  }

  // Update user profile
  static async updateUserProfile(uid: string, updates: Partial<UserProfile>) {
    try {
      return await FirebaseService.updateUserProfile(uid, updates);
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  // Create user profile
  static async createUserProfile(uid: string, email: string, displayName: string) {
    try {
      const userProfile: Partial<UserProfile> = {
        uid,
        email,
        displayName,
        role: 'user',
        loyaltyPoints: 0,
        pastBookings: [],
        preferences: {
          accommodation: 'midrange',
          activities: [],
          dietaryRestrictions: [],
          fitnessLevel: 'moderate',
          photographyInterest: false,
          birdingInterest: false
        }
      };

      return await FirebaseService.updateUserProfile(uid, userProfile);
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  }

  // Get user bookings
  static async getUserBookings(userId: string) {
    try {
      return await FirebaseService.getBookings(userId);
    } catch (error) {
      console.error('Error getting user bookings:', error);
      throw error;
    }
  }

  // Get user wishlist
  static async getUserWishlist(userId: string) {
    try {
      return await FirebaseService.getUserWishlist(userId);
    } catch (error) {
      console.error('Error getting user wishlist:', error);
      throw error;
    }
  }

  // Update user wishlist
  static async updateWishlist(userId: string, tourIds: string[], destinationIds: string[] = []) {
    try {
      return await FirebaseService.updateUserWishlist(userId, tourIds, destinationIds);
    } catch (error) {
      console.error('Error updating wishlist:', error);
      throw error;
    }
  }

  // Get user notifications
  static async getUserNotifications(userId: string) {
    try {
      return await FirebaseService.getUserNotifications(userId);
    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw error;
    }
  }

  // Mark notification as read
  static async markNotificationAsRead(notificationId: string) {
    try {
      return await FirebaseService.markNotificationAsRead(notificationId);
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }
}
