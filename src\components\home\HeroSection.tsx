import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, MapPin, Calendar, Users, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import '@/utils/testQuickSearch';

const HeroSection = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentSlide, setCurrentSlide] = useState(0);

  // Search form state
  const [searchForm, setSearchForm] = useState({
    destination: '',
    date: '',
    travelers: '2'
  });
  const [isSearching, setIsSearching] = useState(false);

  const slides = [
    {
      image: 'photo-1472396961693-142e6e269027',
      title: 'Experience the Magic of Tanzania Safari',
      subtitle: 'Witness the Great Migration and encounter Africa\'s Big Five in their natural habitat',
    },
    {
      image: 'photo-1466721591366-2d5fba72006d',
      title: 'Luxury Safari Adventures Await',
      subtitle: 'Premium accommodations and expert guides for an unforgettable journey',
    },
    {
      image: 'photo-1493962853295-0fd70327578a',
      title: 'Cultural Immersion & Wildlife',
      subtitle: 'Connect with local communities while exploring pristine wilderness',
    }
  ];

  // Popular destinations and search tags
  const popularDestinations = [
    'Serengeti National Park',
    'Ngorongoro Crater',
    'Tarangire National Park',
    'Lake Manyara',
    'Mount Kilimanjaro',
    'Maasai Villages'
  ];

  const popularTags = [
    { label: 'Serengeti Safari', destination: 'Serengeti National Park' },
    { label: 'Kilimanjaro Trek', destination: 'Mount Kilimanjaro' },
    { label: 'Ngorongoro Crater', destination: 'Ngorongoro Crater' },
    { label: 'Cultural Tours', destination: 'Maasai Villages' }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [slides.length]);

  // Handle form input changes
  const handleInputChange = (field: string, value: string) => {
    setSearchForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle search form submission
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!searchForm.destination.trim()) {
      toast({
        title: "Destination Required",
        description: "Please select a destination for your safari adventure.",
        variant: "destructive"
      });
      return;
    }

    setIsSearching(true);

    try {
      // Create search parameters
      const searchParams = new URLSearchParams();

      if (searchForm.destination) {
        searchParams.set('destination', searchForm.destination);
        searchParams.set('search', searchForm.destination); // Also set as general search term
      }

      if (searchForm.date) {
        searchParams.set('date', searchForm.date);
      }

      if (searchForm.travelers && searchForm.travelers !== '2') {
        searchParams.set('travelers', searchForm.travelers);
      }

      // Navigate to tours page with search parameters
      navigate(`/tours?${searchParams.toString()}`);

      toast({
        title: "Searching Tours",
        description: `Finding safari tours for ${searchForm.destination}...`,
      });
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search Error",
        description: "Unable to search tours. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Handle popular tag clicks
  const handleTagClick = (tag: typeof popularTags[0]) => {
    setSearchForm(prev => ({
      ...prev,
      destination: tag.destination
    }));

    // Auto-submit search when tag is clicked
    const searchParams = new URLSearchParams();
    searchParams.set('destination', tag.destination);
    searchParams.set('search', tag.destination);

    navigate(`/tours?${searchParams.toString()}`);

    toast({
      title: "Searching Tours",
      description: `Finding ${tag.label} experiences...`,
    });
  };

  return (
    <section className="relative h-screen min-h-[600px] overflow-hidden">
      {/* Background Slider */}
      <div className="absolute inset-0">
        {slides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={`https://images.unsplash.com/${slide.image}?auto=format&fit=crop&w=1920&h=1080`}
              alt={slide.title}
              className="h-full w-full object-cover"
            />
            <div className="absolute inset-0 bg-black/40" />
          </div>
        ))}
      </div>

      {/* Content */}
      <div className="relative z-10 flex h-full items-center justify-center">
        <div className="container mx-auto px-4 text-center text-white">
          {/* --- MODIFIED LINE --- */}
          <h1 className="mb-6 text-5xl md:text-7xl leading-tight font-bubblegum drop-shadow-lg">
            {slides[currentSlide].title}
          </h1>
          {/* --- END MODIFICATION --- */}
          
          <p className="mb-8 text-lg md:text-xl max-w-2xl mx-auto opacity-90">
            {slides[currentSlide].subtitle}
          </p>

          {/* Quick Search Form */}
          <form onSubmit={handleSearch} className="max-w-4xl mx-auto bg-white/95 backdrop-blur rounded-2xl p-6 shadow-2xl">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <Input
                  list="destinations"
                  placeholder="Where to go?"
                  value={searchForm.destination}
                  onChange={(e) => handleInputChange('destination', e.target.value)}
                  className="pl-10 h-12 text-gray-900 border-gray-200 focus:border-orange-500"
                  aria-label="Destination"
                  autoComplete="off"
                  required
                />
                <datalist id="destinations">
                  {popularDestinations.map((dest) => (
                    <option key={dest} value={dest} />
                  ))}
                </datalist>
              </div>

              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <Input
                  type="date"
                  value={searchForm.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className="pl-10 h-12 text-gray-900 border-gray-200 focus:border-orange-500"
                  aria-label="Travel date"
                />
              </div>

              <div className="relative">
                <Users className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <select
                  value={searchForm.travelers}
                  onChange={(e) => handleInputChange('travelers', e.target.value)}
                  className="w-full h-12 pl-10 pr-4 text-gray-900 border border-gray-200 rounded-md focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-200"
                  aria-label="Number of travelers"
                >
                  <option value="1">1 Traveler</option>
                  <option value="2">2 Travelers</option>
                  <option value="3">3 Travelers</option>
                  <option value="4">4 Travelers</option>
                  <option value="5">5 Travelers</option>
                  <option value="6">6+ Travelers</option>
                </select>
              </div>

              <Button
                type="submit"
                disabled={isSearching}
                className="h-12 bg-orange-600 hover:bg-orange-700 text-white font-semibold disabled:opacity-50"
              >
                {isSearching ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="mr-2 h-5 w-5" />
                    Search Tours
                  </>
                )}
              </Button>
            </div>

            <div className="flex flex-wrap gap-2 justify-center">
              <span className="text-sm text-gray-600">Popular:</span>
              {popularTags.map((tag) => (
                <button
                  key={tag.label}
                  type="button"
                  onClick={() => handleTagClick(tag)}
                  className="px-3 py-1 bg-gray-100 hover:bg-orange-100 text-gray-700 text-sm rounded-full transition-colors hover:shadow-md"
                >
                  {tag.label}
                </button>
              ))}
            </div>
          </form>

          <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
            <Button
              size="lg"
              onClick={() => navigate('/tours')}
              className="bg-orange-600 hover:bg-orange-700 text-white"
            >
              Explore All Tours
            </Button>
            <Button
              size="lg"
              variant="outline"
              onClick={() => navigate('/tour-builder')}
              className="text-white border-white hover:bg-white hover:text-gray-900"
            >
              Plan Custom Trip
            </Button>
          </div>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`h-2 w-8 rounded-full transition-all ${
              index === currentSlide ? 'bg-white' : 'bg-white/50'
            }`}
          />
        ))}
      </div>
    </section>
  );
};

export default HeroSection;